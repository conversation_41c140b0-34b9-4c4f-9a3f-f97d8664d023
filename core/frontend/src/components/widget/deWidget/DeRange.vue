<template>
  <div class="date-selector">
    <div class="left-panel">
      <el-radio-group
        v-model="analysisType"
        size="large"
        @change="typeChange"
      >
        <el-radio-button label="yearrange">年度分析</el-radio-button>
        <el-radio-button label="quarterrange">季度分析</el-radio-button>
        <el-radio-button label="monthrange">月度分析</el-radio-button>
      </el-radio-group>
    </div>
    <div
      v-if="!isConfig"
      class="right-panel"
    >
      <el-date-picker
        :key="analysisType"
        v-model="selectedValue"
        :class="{ 'show-required-tips': showRequiredTips }"
        :type="analysisType"
        placeholder="请选择"
        :clearable="false"
        :editable="false"
        :append-to-body="inScreen"
        @change="dateChange"
      />
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'

export default {
  name: 'DateSelector',
  props: {
    canvasId: {
      type: String,
      required: true
    },
    element: {
      type: Object,
      default: null
    },
    isConfig: {
      type: Boolean,
      default: false
    },
    inScreen: {
      type: Boolean,
      required: false,
      default: true
    },
    isRelation: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      analysisType: this.element.options.attrs.analysisType,
      selectedValue: [dayjs().startOf('year'), dayjs()]
    }
  },
  computed: {
    showRequiredTips() {
      return (
        this.element.options.attrs.required &&
        (!this.selectedValue || this.selectedValue.length === 0)
      )
    }
  },
  watch: {
  },
  methods: {
    typeChange(value) {
      if (!this.isConfig) {
        this.dateChange()
      } else {
        this.element.options.attrs.analysisType = value
      }
    },
    dateChange() {
      if (this.selectedValue === null) {
        this.element.options.value = ''
      } else {
        const [start, end] = this.selectedValue
        const unit = ({
          'yearrange': 'year',
          'quarterrange': 'month',
          'monthrange': 'month'
        })[this.analysisType]
        this.element.options.value = [
          dayjs(start).startOf(unit).valueOf(),
          dayjs(end).endOf(unit).valueOf()
        ]
      }
      if (!this.showRequiredTips) {
        this.$store.commit('setLastValidFilters', {
          componentId: this.element.id,
          val: this.element.options.value
        })
      }
      this.setCondition()
    },
    getCondition() {
      const param = {
        canvasId: this.canvasId,
        component: this.element,
        value: this.element.options.value,
        operator: 'aggregation-' + this.analysisType
      }
      return param
    },
    setCondition() {
      if (this.showRequiredTips) {
        return
      }
      const param = this.getCondition()
      !this.isRelation &&
        this.$store.commit('addViewFilter', param)
    }
  }
}
</script>

<style scoped>
.date-selector {
  display: flex;
  align-items: center;
}
.left-panel {
  margin-right: 20px;
}
.right-panel {
  flex: 1;
}
</style>

{"name": "dataease", "version": "1.18.26", "description": "dataease front", "private": true, "scripts": {"dev": "rsbuild dev", "build": "rsbuild build", "serves": "node --max_old_space_size=4096 ./node_modules/.bin/rsbuild build", "build:prod": "rsbuild build --mode production", "build:stage": "rsbuild build --mode staging", "dll": "rsbuild build --mode production --config ./webpack.dll.conf.js", "preview": "rsbuild preview", "lint": "eslint --ext .js,.vue src --fix", "lint-staged": "lint-staged", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml"}, "husky": {"hooks": {"pre-commit": "npm run lint-staged"}}, "lint-staged": {"src/**/*.js": "eslint --ext .js", "src/**/*.vue": "eslint --ext .vue"}, "dependencies": {"@antv/g2plot": "^2.4.9", "@antv/l7": "2.15.0", "@antv/l7-component": "2.15.0", "@antv/l7-core": "2.15.0", "@antv/l7-layers": "2.15.0", "@antv/l7-maps": "2.15.0", "@antv/l7-renderer": "2.15.0", "@antv/l7-scene": "2.15.0", "@antv/l7-source": "2.15.0", "@antv/l7-utils": "2.15.0", "@antv/s2": "^1.54.6", "@antv/util": "^2.0.17", "@riophae/vue-treeselect": "0.4.0", "@tinymce/tinymce-vue": "^3.2.8", "axios": "^1.6.1", "dayjs": "^1.11.10", "echarts": "^5.0.1", "element-resize-detector": "^1.2.3", "element-ui": "2.15.7", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "fit2cloud-ui": "^1.8.0", "flv.js": "^1.6.2", "html-to-image": "^1.11.11", "html2canvasde": "^v1.1.4-de", "jquery": "^3.1.1", "js-base64": "^3.7.2", "js-cookie": "2.2.0", "js-pinyin": "^0.1.9", "jsencrypt": "^3.0.0-rc.1", "jspdf": "^2.3.1", "jszip": "^3.10.1", "lodash": "^4.17.21", "lodash.isboolean": "^3.0.3", "lodash.isempty": "^4.4.0", "lodash.isinteger": "^4.0.4", "lodash.isnull": "^3.0.0", "lodash.isnumber": "^3.0.3", "lodash.isobject": "^3.0.2", "lodash.isstring": "^4.0.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "screenfull": "4.2.0", "sockjs-client": "^1.6.0", "stompjs": "^2.3.3", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "tinymce": "^5.8.2", "umy-ui": "^1.1.6", "uuid": "^9.0.1", "vant": "^2.13.2", "vue": "2.6.10", "vue-clipboard2": "0.3.1", "vue-codemirror": "^4.0.6", "vue-friendly-iframe": "^0.20.0", "vue-fullscreen": "^2.5.2", "vue-i18n": "7.3.2", "vue-json-views": "^1.3.0", "vue-proportion-directive": "^1.1.0", "vue-router": "3.0.6", "vue-to-pdf": "^1.0.0", "vue-uuid": "2.0.2", "vue-video-player": "^5.0.2", "vue2-ace-editor": "0.0.15", "vuedraggable": "^2.24.3", "vuex": "3.1.0", "webpack": "^4.46.0", "xlsx": "https://cdn.sheetjs.com/xlsx-0.19.3/xlsx-0.19.3.tgz", "xss": "^1.0.14"}, "devDependencies": {"@babel/core": "^7.4.0-0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.18.6", "@babel/register": "7.0.0", "@rsbuild/core": "^1.4.7", "@rsbuild/plugin-less": "^1.2.5", "@rsbuild/plugin-sass": "^1.3.3", "@rsbuild/plugin-vue2": "^1.0.3", "@vue/runtime-dom": "^3.5.13", "add-asset-html-webpack-plugin": "^3.1.3", "autoprefixer": "^10.4.21", "babel-eslint": "^10.1.0", "chalk": "2.4.2", "clean-webpack-plugin": "^1.0.1", "connect": "3.6.6", "copy-webpack-plugin": "^4.6.0", "eslint": "^7.32.0", "eslint-config-prettier": "^8.5.0", "eslint-loader": "^4.0.2", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^9.1.0", "html-webpack-plugin": "3.2.0", "husky": "^8.0.1", "less": "^3.0.0", "less-loader": "^5.0.0", "lint-staged": "^13.0.1", "mockjs": "1.0.1-beta3", "postcss": "^8.5.6", "runjs": "^4.1.3", "sass": "^1.33.0", "sass-loader": "^7.1.0", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "^0.7.2", "serve-static": "^1.13.2", "vue-template-compiler": "2.6.10", "vuetify": "^2.6.6", "webpack-cli": "^3.2.3"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}
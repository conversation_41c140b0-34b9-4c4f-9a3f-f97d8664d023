const { defineConfig } = require('@rsbuild/core')
const { pluginVue2 } = require('@rsbuild/plugin-vue2')
const { pluginSass } = require('@rsbuild/plugin-sass')
const { pluginLess } = require('@rsbuild/plugin-less')
const path = require('path')
const pkg = require('./package.json')

const defaultSettings = require('./src/settings.js')
const name = defaultSettings.title || 'vue Admin Template'
const port = process.env.port || process.env.npm_config_port || 9528

module.exports = defineConfig({
  plugins: [
    pluginVue2(),
    pluginSass({
      sassLoaderOptions: {
        additionalData: `@import "@/style/index.scss";`,
        silenceDeprecations: ['legacy-js-api', 'function-units', 'import', 'global-builtin', 'slash-div', 'bogus-combinators']
      }
    }),
    pluginLess()
  ],

  // 多页面应用配置
  source: {
    entry: {
      index: './src/main.js',
      mobile: './src/mobile/main.js'
    }
  },

  // 路径别名配置
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },

  // HTML 模板配置
  html: {
    template: ({ entryName }) => {
      const templates = {
        index: './public/index.html',
        mobile: './public/mobile.html'
      }
      return templates[entryName] || './public/index.html'
    },
    title: name
  },

  // 输出配置
  output: {
    distPath: {
      root: 'dist'
    },
    filename: {
      js: process.env.NODE_ENV === 'development'
        ? 'js/[name].js'
        : `js/[name].[contenthash:8].${pkg.version}.js`,
      css: process.env.NODE_ENV === 'development'
        ? 'css/[name].css'
        : `css/[name].[contenthash:8].${pkg.version}.css`
    },
    sourceMap: {
      js: process.env.NODE_ENV === 'development' ? 'cheap-module-source-map' : 'source-map'
    }
  },

  // 开发服务器配置
  server: {
    port: port,
    open: true,
    host: 'localhost'
  },

  // 构建工具配置
  tools: {
    rspack: (config, { addRules, mergeConfig, rspack }) => {
      // 处理 SVG sprite loader
      addRules([
        {
          test: /\.svg$/,
          include: [path.resolve(__dirname, 'src/icons')],
          use: [
            {
              loader: 'svg-sprite-loader',
              options: {
                symbolId: 'icon-[name]'
              }
            }
          ]
        },
        {
          test: /\.svg$/,
          include: [path.resolve(__dirname, 'src/deicons')],
          use: [
            {
              loader: 'svg-sprite-loader',
              options: {
                symbolId: '[name]'
              }
            }
          ]
        }
      ])

      // 复制静态文件
      config.plugins.push(
        new rspack.CopyRspackPlugin([
          {
            from: path.join(__dirname, 'static'),
            to: path.join(__dirname, 'dist/static')
          }
        ])
      )
      // dll
      config.plugins.push(
        new rspack.DllReferencePlugin({
          context: process.cwd(),
          manifest: require('./public/vendor/vendor-manifest.json')
        })
      )

      return config
    }
  },

  // 性能配置
  performance: {
    chunkSplit: {
      strategy: 'split-by-experience'
    }
  },

  // 环境变量
  environments: {
    development: {
      output: {
        sourceMap: {
          js: 'cheap-module-source-map'
        }
      }
    },
    production: {
      output: {
        sourceMap: {
          js: 'source-map'
        }
      },
      performance: {
        removeConsole: true
      }
    }
  }
})
